"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/superjson";
exports.ids = ["vendor-chunks/superjson"];
exports.modules = {

/***/ "(ssr)/./node_modules/superjson/dist/accessDeep.js":
/*!***************************************************!*\
  !*** ./node_modules/superjson/dist/accessDeep.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDeep: () => (/* binding */ getDeep),\n/* harmony export */   setDeep: () => (/* binding */ setDeep)\n/* harmony export */ });\n/* harmony import */ var _is_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is.js */ \"(ssr)/./node_modules/superjson/dist/is.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/superjson/dist/util.js\");\n\n\nconst getNthKey = (value, n) => {\n    if (n > value.size)\n        throw new Error('index out of bounds');\n    const keys = value.keys();\n    while (n > 0) {\n        keys.next();\n        n--;\n    }\n    return keys.next().value;\n};\nfunction validatePath(path) {\n    if ((0,_util_js__WEBPACK_IMPORTED_MODULE_1__.includes)(path, '__proto__')) {\n        throw new Error('__proto__ is not allowed as a property');\n    }\n    if ((0,_util_js__WEBPACK_IMPORTED_MODULE_1__.includes)(path, 'prototype')) {\n        throw new Error('prototype is not allowed as a property');\n    }\n    if ((0,_util_js__WEBPACK_IMPORTED_MODULE_1__.includes)(path, 'constructor')) {\n        throw new Error('constructor is not allowed as a property');\n    }\n}\nconst getDeep = (object, path) => {\n    validatePath(path);\n    for (let i = 0; i < path.length; i++) {\n        const key = path[i];\n        if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isSet)(object)) {\n            object = getNthKey(object, +key);\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isMap)(object)) {\n            const row = +key;\n            const type = +path[++i] === 0 ? 'key' : 'value';\n            const keyOfRow = getNthKey(object, row);\n            switch (type) {\n                case 'key':\n                    object = keyOfRow;\n                    break;\n                case 'value':\n                    object = object.get(keyOfRow);\n                    break;\n            }\n        }\n        else {\n            object = object[key];\n        }\n    }\n    return object;\n};\nconst setDeep = (object, path, mapper) => {\n    validatePath(path);\n    if (path.length === 0) {\n        return mapper(object);\n    }\n    let parent = object;\n    for (let i = 0; i < path.length - 1; i++) {\n        const key = path[i];\n        if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(parent)) {\n            const index = +key;\n            parent = parent[index];\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(parent)) {\n            parent = parent[key];\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isSet)(parent)) {\n            const row = +key;\n            parent = getNthKey(parent, row);\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isMap)(parent)) {\n            const isEnd = i === path.length - 2;\n            if (isEnd) {\n                break;\n            }\n            const row = +key;\n            const type = +path[++i] === 0 ? 'key' : 'value';\n            const keyOfRow = getNthKey(parent, row);\n            switch (type) {\n                case 'key':\n                    parent = keyOfRow;\n                    break;\n                case 'value':\n                    parent = parent.get(keyOfRow);\n                    break;\n            }\n        }\n    }\n    const lastKey = path[path.length - 1];\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(parent)) {\n        parent[+lastKey] = mapper(parent[+lastKey]);\n    }\n    else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(parent)) {\n        parent[lastKey] = mapper(parent[lastKey]);\n    }\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isSet)(parent)) {\n        const oldValue = getNthKey(parent, +lastKey);\n        const newValue = mapper(oldValue);\n        if (oldValue !== newValue) {\n            parent.delete(oldValue);\n            parent.add(newValue);\n        }\n    }\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isMap)(parent)) {\n        const row = +path[path.length - 2];\n        const keyToRow = getNthKey(parent, row);\n        const type = +lastKey === 0 ? 'key' : 'value';\n        switch (type) {\n            case 'key': {\n                const newKey = mapper(keyToRow);\n                parent.set(newKey, parent.get(keyToRow));\n                if (newKey !== keyToRow) {\n                    parent.delete(keyToRow);\n                }\n                break;\n            }\n            case 'value': {\n                parent.set(keyToRow, mapper(parent.get(keyToRow)));\n                break;\n            }\n        }\n    }\n    return object;\n};\n//# sourceMappingURL=accessDeep.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/accessDeep.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/class-registry.js":
/*!*******************************************************!*\
  !*** ./node_modules/superjson/dist/class-registry.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClassRegistry: () => (/* binding */ ClassRegistry)\n/* harmony export */ });\n/* harmony import */ var _registry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./registry.js */ \"(ssr)/./node_modules/superjson/dist/registry.js\");\n\nclass ClassRegistry extends _registry_js__WEBPACK_IMPORTED_MODULE_0__.Registry {\n    constructor() {\n        super(c => c.name);\n        this.classToAllowedProps = new Map();\n    }\n    register(value, options) {\n        if (typeof options === 'object') {\n            if (options.allowProps) {\n                this.classToAllowedProps.set(value, options.allowProps);\n            }\n            super.register(value, options.identifier);\n        }\n        else {\n            super.register(value, options);\n        }\n    }\n    getAllowedProps(value) {\n        return this.classToAllowedProps.get(value);\n    }\n}\n//# sourceMappingURL=class-registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvY2xhc3MtcmVnaXN0cnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUM7QUFDbEMsNEJBQTRCLGtEQUFRO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc3VwcmVldGhcXE9uZURyaXZlXFxEZXNrdG9wXFxhYmNkd2VyXFxOYXNoaXJhXFxub2RlX21vZHVsZXNcXHN1cGVyanNvblxcZGlzdFxcY2xhc3MtcmVnaXN0cnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUmVnaXN0cnkgfSBmcm9tICcuL3JlZ2lzdHJ5LmpzJztcbmV4cG9ydCBjbGFzcyBDbGFzc1JlZ2lzdHJ5IGV4dGVuZHMgUmVnaXN0cnkge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlcihjID0+IGMubmFtZSk7XG4gICAgICAgIHRoaXMuY2xhc3NUb0FsbG93ZWRQcm9wcyA9IG5ldyBNYXAoKTtcbiAgICB9XG4gICAgcmVnaXN0ZXIodmFsdWUsIG9wdGlvbnMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBvcHRpb25zID09PSAnb2JqZWN0Jykge1xuICAgICAgICAgICAgaWYgKG9wdGlvbnMuYWxsb3dQcm9wcykge1xuICAgICAgICAgICAgICAgIHRoaXMuY2xhc3NUb0FsbG93ZWRQcm9wcy5zZXQodmFsdWUsIG9wdGlvbnMuYWxsb3dQcm9wcyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBzdXBlci5yZWdpc3Rlcih2YWx1ZSwgb3B0aW9ucy5pZGVudGlmaWVyKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHN1cGVyLnJlZ2lzdGVyKHZhbHVlLCBvcHRpb25zKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBnZXRBbGxvd2VkUHJvcHModmFsdWUpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xhc3NUb0FsbG93ZWRQcm9wcy5nZXQodmFsdWUpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNsYXNzLXJlZ2lzdHJ5LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/class-registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/custom-transformer-registry.js":
/*!********************************************************************!*\
  !*** ./node_modules/superjson/dist/custom-transformer-registry.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomTransformerRegistry: () => (/* binding */ CustomTransformerRegistry)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/superjson/dist/util.js\");\n\nclass CustomTransformerRegistry {\n    constructor() {\n        this.transfomers = {};\n    }\n    register(transformer) {\n        this.transfomers[transformer.name] = transformer;\n    }\n    findApplicable(v) {\n        return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.find)(this.transfomers, transformer => transformer.isApplicable(v));\n    }\n    findByName(name) {\n        return this.transfomers[name];\n    }\n}\n//# sourceMappingURL=custom-transformer-registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvY3VzdG9tLXRyYW5zZm9ybWVyLXJlZ2lzdHJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlDO0FBQzFCO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLDhDQUFJO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzdXByZWV0aFxcT25lRHJpdmVcXERlc2t0b3BcXGFiY2R3ZXJcXE5hc2hpcmFcXG5vZGVfbW9kdWxlc1xcc3VwZXJqc29uXFxkaXN0XFxjdXN0b20tdHJhbnNmb3JtZXItcmVnaXN0cnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZmluZCB9IGZyb20gJy4vdXRpbC5qcyc7XG5leHBvcnQgY2xhc3MgQ3VzdG9tVHJhbnNmb3JtZXJSZWdpc3RyeSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMudHJhbnNmb21lcnMgPSB7fTtcbiAgICB9XG4gICAgcmVnaXN0ZXIodHJhbnNmb3JtZXIpIHtcbiAgICAgICAgdGhpcy50cmFuc2ZvbWVyc1t0cmFuc2Zvcm1lci5uYW1lXSA9IHRyYW5zZm9ybWVyO1xuICAgIH1cbiAgICBmaW5kQXBwbGljYWJsZSh2KSB7XG4gICAgICAgIHJldHVybiBmaW5kKHRoaXMudHJhbnNmb21lcnMsIHRyYW5zZm9ybWVyID0+IHRyYW5zZm9ybWVyLmlzQXBwbGljYWJsZSh2KSk7XG4gICAgfVxuICAgIGZpbmRCeU5hbWUobmFtZSkge1xuICAgICAgICByZXR1cm4gdGhpcy50cmFuc2ZvbWVyc1tuYW1lXTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jdXN0b20tdHJhbnNmb3JtZXItcmVnaXN0cnkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/custom-transformer-registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/double-indexed-kv.js":
/*!**********************************************************!*\
  !*** ./node_modules/superjson/dist/double-indexed-kv.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DoubleIndexedKV: () => (/* binding */ DoubleIndexedKV)\n/* harmony export */ });\nclass DoubleIndexedKV {\n    constructor() {\n        this.keyToValue = new Map();\n        this.valueToKey = new Map();\n    }\n    set(key, value) {\n        this.keyToValue.set(key, value);\n        this.valueToKey.set(value, key);\n    }\n    getByKey(key) {\n        return this.keyToValue.get(key);\n    }\n    getByValue(value) {\n        return this.valueToKey.get(value);\n    }\n    clear() {\n        this.keyToValue.clear();\n        this.valueToKey.clear();\n    }\n}\n//# sourceMappingURL=double-indexed-kv.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvZG91YmxlLWluZGV4ZWQta3YuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzdXByZWV0aFxcT25lRHJpdmVcXERlc2t0b3BcXGFiY2R3ZXJcXE5hc2hpcmFcXG5vZGVfbW9kdWxlc1xcc3VwZXJqc29uXFxkaXN0XFxkb3VibGUtaW5kZXhlZC1rdi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgRG91YmxlSW5kZXhlZEtWIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgdGhpcy5rZXlUb1ZhbHVlID0gbmV3IE1hcCgpO1xuICAgICAgICB0aGlzLnZhbHVlVG9LZXkgPSBuZXcgTWFwKCk7XG4gICAgfVxuICAgIHNldChrZXksIHZhbHVlKSB7XG4gICAgICAgIHRoaXMua2V5VG9WYWx1ZS5zZXQoa2V5LCB2YWx1ZSk7XG4gICAgICAgIHRoaXMudmFsdWVUb0tleS5zZXQodmFsdWUsIGtleSk7XG4gICAgfVxuICAgIGdldEJ5S2V5KGtleSkge1xuICAgICAgICByZXR1cm4gdGhpcy5rZXlUb1ZhbHVlLmdldChrZXkpO1xuICAgIH1cbiAgICBnZXRCeVZhbHVlKHZhbHVlKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnZhbHVlVG9LZXkuZ2V0KHZhbHVlKTtcbiAgICB9XG4gICAgY2xlYXIoKSB7XG4gICAgICAgIHRoaXMua2V5VG9WYWx1ZS5jbGVhcigpO1xuICAgICAgICB0aGlzLnZhbHVlVG9LZXkuY2xlYXIoKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kb3VibGUtaW5kZXhlZC1rdi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/double-indexed-kv.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/index.js":
/*!**********************************************!*\
  !*** ./node_modules/superjson/dist/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SuperJSON: () => (/* binding */ SuperJSON),\n/* harmony export */   allowErrorProps: () => (/* binding */ allowErrorProps),\n/* harmony export */   \"default\": () => (/* binding */ SuperJSON),\n/* harmony export */   deserialize: () => (/* binding */ deserialize),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   registerClass: () => (/* binding */ registerClass),\n/* harmony export */   registerCustom: () => (/* binding */ registerCustom),\n/* harmony export */   registerSymbol: () => (/* binding */ registerSymbol),\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\n/* harmony import */ var _class_registry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./class-registry.js */ \"(ssr)/./node_modules/superjson/dist/class-registry.js\");\n/* harmony import */ var _registry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./registry.js */ \"(ssr)/./node_modules/superjson/dist/registry.js\");\n/* harmony import */ var _custom_transformer_registry_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./custom-transformer-registry.js */ \"(ssr)/./node_modules/superjson/dist/custom-transformer-registry.js\");\n/* harmony import */ var _plainer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./plainer.js */ \"(ssr)/./node_modules/superjson/dist/plainer.js\");\n/* harmony import */ var copy_anything__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! copy-anything */ \"(ssr)/./node_modules/copy-anything/dist/index.js\");\n\n\n\n\n\nclass SuperJSON {\n    /**\n     * @param dedupeReferentialEqualities  If true, SuperJSON will make sure only one instance of referentially equal objects are serialized and the rest are replaced with `null`.\n     */\n    constructor({ dedupe = false, } = {}) {\n        this.classRegistry = new _class_registry_js__WEBPACK_IMPORTED_MODULE_0__.ClassRegistry();\n        this.symbolRegistry = new _registry_js__WEBPACK_IMPORTED_MODULE_1__.Registry(s => s.description ?? '');\n        this.customTransformerRegistry = new _custom_transformer_registry_js__WEBPACK_IMPORTED_MODULE_2__.CustomTransformerRegistry();\n        this.allowedErrorProps = [];\n        this.dedupe = dedupe;\n    }\n    serialize(object) {\n        const identities = new Map();\n        const output = (0,_plainer_js__WEBPACK_IMPORTED_MODULE_3__.walker)(object, identities, this, this.dedupe);\n        const res = {\n            json: output.transformedValue,\n        };\n        if (output.annotations) {\n            res.meta = {\n                ...res.meta,\n                values: output.annotations,\n            };\n        }\n        const equalityAnnotations = (0,_plainer_js__WEBPACK_IMPORTED_MODULE_3__.generateReferentialEqualityAnnotations)(identities, this.dedupe);\n        if (equalityAnnotations) {\n            res.meta = {\n                ...res.meta,\n                referentialEqualities: equalityAnnotations,\n            };\n        }\n        return res;\n    }\n    deserialize(payload) {\n        const { json, meta } = payload;\n        let result = (0,copy_anything__WEBPACK_IMPORTED_MODULE_4__.copy)(json);\n        if (meta?.values) {\n            result = (0,_plainer_js__WEBPACK_IMPORTED_MODULE_3__.applyValueAnnotations)(result, meta.values, this);\n        }\n        if (meta?.referentialEqualities) {\n            result = (0,_plainer_js__WEBPACK_IMPORTED_MODULE_3__.applyReferentialEqualityAnnotations)(result, meta.referentialEqualities);\n        }\n        return result;\n    }\n    stringify(object) {\n        return JSON.stringify(this.serialize(object));\n    }\n    parse(string) {\n        return this.deserialize(JSON.parse(string));\n    }\n    registerClass(v, options) {\n        this.classRegistry.register(v, options);\n    }\n    registerSymbol(v, identifier) {\n        this.symbolRegistry.register(v, identifier);\n    }\n    registerCustom(transformer, name) {\n        this.customTransformerRegistry.register({\n            name,\n            ...transformer,\n        });\n    }\n    allowErrorProps(...props) {\n        this.allowedErrorProps.push(...props);\n    }\n}\nSuperJSON.defaultInstance = new SuperJSON();\nSuperJSON.serialize = SuperJSON.defaultInstance.serialize.bind(SuperJSON.defaultInstance);\nSuperJSON.deserialize = SuperJSON.defaultInstance.deserialize.bind(SuperJSON.defaultInstance);\nSuperJSON.stringify = SuperJSON.defaultInstance.stringify.bind(SuperJSON.defaultInstance);\nSuperJSON.parse = SuperJSON.defaultInstance.parse.bind(SuperJSON.defaultInstance);\nSuperJSON.registerClass = SuperJSON.defaultInstance.registerClass.bind(SuperJSON.defaultInstance);\nSuperJSON.registerSymbol = SuperJSON.defaultInstance.registerSymbol.bind(SuperJSON.defaultInstance);\nSuperJSON.registerCustom = SuperJSON.defaultInstance.registerCustom.bind(SuperJSON.defaultInstance);\nSuperJSON.allowErrorProps = SuperJSON.defaultInstance.allowErrorProps.bind(SuperJSON.defaultInstance);\n\nconst serialize = SuperJSON.serialize;\nconst deserialize = SuperJSON.deserialize;\nconst stringify = SuperJSON.stringify;\nconst parse = SuperJSON.parse;\nconst registerClass = SuperJSON.registerClass;\nconst registerCustom = SuperJSON.registerCustom;\nconst registerSymbol = SuperJSON.registerSymbol;\nconst allowErrorProps = SuperJSON.allowErrorProps;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/is.js":
/*!*******************************************!*\
  !*** ./node_modules/superjson/dist/is.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isArray: () => (/* binding */ isArray),\n/* harmony export */   isBigint: () => (/* binding */ isBigint),\n/* harmony export */   isBoolean: () => (/* binding */ isBoolean),\n/* harmony export */   isDate: () => (/* binding */ isDate),\n/* harmony export */   isEmptyObject: () => (/* binding */ isEmptyObject),\n/* harmony export */   isError: () => (/* binding */ isError),\n/* harmony export */   isInfinite: () => (/* binding */ isInfinite),\n/* harmony export */   isMap: () => (/* binding */ isMap),\n/* harmony export */   isNaNValue: () => (/* binding */ isNaNValue),\n/* harmony export */   isNull: () => (/* binding */ isNull),\n/* harmony export */   isNumber: () => (/* binding */ isNumber),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isPrimitive: () => (/* binding */ isPrimitive),\n/* harmony export */   isRegExp: () => (/* binding */ isRegExp),\n/* harmony export */   isSet: () => (/* binding */ isSet),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   isSymbol: () => (/* binding */ isSymbol),\n/* harmony export */   isTypedArray: () => (/* binding */ isTypedArray),\n/* harmony export */   isURL: () => (/* binding */ isURL),\n/* harmony export */   isUndefined: () => (/* binding */ isUndefined)\n/* harmony export */ });\nconst getType = (payload) => Object.prototype.toString.call(payload).slice(8, -1);\nconst isUndefined = (payload) => typeof payload === 'undefined';\nconst isNull = (payload) => payload === null;\nconst isPlainObject = (payload) => {\n    if (typeof payload !== 'object' || payload === null)\n        return false;\n    if (payload === Object.prototype)\n        return false;\n    if (Object.getPrototypeOf(payload) === null)\n        return true;\n    return Object.getPrototypeOf(payload) === Object.prototype;\n};\nconst isEmptyObject = (payload) => isPlainObject(payload) && Object.keys(payload).length === 0;\nconst isArray = (payload) => Array.isArray(payload);\nconst isString = (payload) => typeof payload === 'string';\nconst isNumber = (payload) => typeof payload === 'number' && !isNaN(payload);\nconst isBoolean = (payload) => typeof payload === 'boolean';\nconst isRegExp = (payload) => payload instanceof RegExp;\nconst isMap = (payload) => payload instanceof Map;\nconst isSet = (payload) => payload instanceof Set;\nconst isSymbol = (payload) => getType(payload) === 'Symbol';\nconst isDate = (payload) => payload instanceof Date && !isNaN(payload.valueOf());\nconst isError = (payload) => payload instanceof Error;\nconst isNaNValue = (payload) => typeof payload === 'number' && isNaN(payload);\nconst isPrimitive = (payload) => isBoolean(payload) ||\n    isNull(payload) ||\n    isUndefined(payload) ||\n    isNumber(payload) ||\n    isString(payload) ||\n    isSymbol(payload);\nconst isBigint = (payload) => typeof payload === 'bigint';\nconst isInfinite = (payload) => payload === Infinity || payload === -Infinity;\nconst isTypedArray = (payload) => ArrayBuffer.isView(payload) && !(payload instanceof DataView);\nconst isURL = (payload) => payload instanceof URL;\n//# sourceMappingURL=is.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvaXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNPO0FBQ0E7QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNBO0FBQ0E7QUFDQTtBQUNQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHN1cHJlZXRoXFxPbmVEcml2ZVxcRGVza3RvcFxcYWJjZHdlclxcTmFzaGlyYVxcbm9kZV9tb2R1bGVzXFxzdXBlcmpzb25cXGRpc3RcXGlzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGdldFR5cGUgPSAocGF5bG9hZCkgPT4gT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKHBheWxvYWQpLnNsaWNlKDgsIC0xKTtcbmV4cG9ydCBjb25zdCBpc1VuZGVmaW5lZCA9IChwYXlsb2FkKSA9PiB0eXBlb2YgcGF5bG9hZCA9PT0gJ3VuZGVmaW5lZCc7XG5leHBvcnQgY29uc3QgaXNOdWxsID0gKHBheWxvYWQpID0+IHBheWxvYWQgPT09IG51bGw7XG5leHBvcnQgY29uc3QgaXNQbGFpbk9iamVjdCA9IChwYXlsb2FkKSA9PiB7XG4gICAgaWYgKHR5cGVvZiBwYXlsb2FkICE9PSAnb2JqZWN0JyB8fCBwYXlsb2FkID09PSBudWxsKVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgaWYgKHBheWxvYWQgPT09IE9iamVjdC5wcm90b3R5cGUpXG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICBpZiAoT2JqZWN0LmdldFByb3RvdHlwZU9mKHBheWxvYWQpID09PSBudWxsKVxuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICByZXR1cm4gT2JqZWN0LmdldFByb3RvdHlwZU9mKHBheWxvYWQpID09PSBPYmplY3QucHJvdG90eXBlO1xufTtcbmV4cG9ydCBjb25zdCBpc0VtcHR5T2JqZWN0ID0gKHBheWxvYWQpID0+IGlzUGxhaW5PYmplY3QocGF5bG9hZCkgJiYgT2JqZWN0LmtleXMocGF5bG9hZCkubGVuZ3RoID09PSAwO1xuZXhwb3J0IGNvbnN0IGlzQXJyYXkgPSAocGF5bG9hZCkgPT4gQXJyYXkuaXNBcnJheShwYXlsb2FkKTtcbmV4cG9ydCBjb25zdCBpc1N0cmluZyA9IChwYXlsb2FkKSA9PiB0eXBlb2YgcGF5bG9hZCA9PT0gJ3N0cmluZyc7XG5leHBvcnQgY29uc3QgaXNOdW1iZXIgPSAocGF5bG9hZCkgPT4gdHlwZW9mIHBheWxvYWQgPT09ICdudW1iZXInICYmICFpc05hTihwYXlsb2FkKTtcbmV4cG9ydCBjb25zdCBpc0Jvb2xlYW4gPSAocGF5bG9hZCkgPT4gdHlwZW9mIHBheWxvYWQgPT09ICdib29sZWFuJztcbmV4cG9ydCBjb25zdCBpc1JlZ0V4cCA9IChwYXlsb2FkKSA9PiBwYXlsb2FkIGluc3RhbmNlb2YgUmVnRXhwO1xuZXhwb3J0IGNvbnN0IGlzTWFwID0gKHBheWxvYWQpID0+IHBheWxvYWQgaW5zdGFuY2VvZiBNYXA7XG5leHBvcnQgY29uc3QgaXNTZXQgPSAocGF5bG9hZCkgPT4gcGF5bG9hZCBpbnN0YW5jZW9mIFNldDtcbmV4cG9ydCBjb25zdCBpc1N5bWJvbCA9IChwYXlsb2FkKSA9PiBnZXRUeXBlKHBheWxvYWQpID09PSAnU3ltYm9sJztcbmV4cG9ydCBjb25zdCBpc0RhdGUgPSAocGF5bG9hZCkgPT4gcGF5bG9hZCBpbnN0YW5jZW9mIERhdGUgJiYgIWlzTmFOKHBheWxvYWQudmFsdWVPZigpKTtcbmV4cG9ydCBjb25zdCBpc0Vycm9yID0gKHBheWxvYWQpID0+IHBheWxvYWQgaW5zdGFuY2VvZiBFcnJvcjtcbmV4cG9ydCBjb25zdCBpc05hTlZhbHVlID0gKHBheWxvYWQpID0+IHR5cGVvZiBwYXlsb2FkID09PSAnbnVtYmVyJyAmJiBpc05hTihwYXlsb2FkKTtcbmV4cG9ydCBjb25zdCBpc1ByaW1pdGl2ZSA9IChwYXlsb2FkKSA9PiBpc0Jvb2xlYW4ocGF5bG9hZCkgfHxcbiAgICBpc051bGwocGF5bG9hZCkgfHxcbiAgICBpc1VuZGVmaW5lZChwYXlsb2FkKSB8fFxuICAgIGlzTnVtYmVyKHBheWxvYWQpIHx8XG4gICAgaXNTdHJpbmcocGF5bG9hZCkgfHxcbiAgICBpc1N5bWJvbChwYXlsb2FkKTtcbmV4cG9ydCBjb25zdCBpc0JpZ2ludCA9IChwYXlsb2FkKSA9PiB0eXBlb2YgcGF5bG9hZCA9PT0gJ2JpZ2ludCc7XG5leHBvcnQgY29uc3QgaXNJbmZpbml0ZSA9IChwYXlsb2FkKSA9PiBwYXlsb2FkID09PSBJbmZpbml0eSB8fCBwYXlsb2FkID09PSAtSW5maW5pdHk7XG5leHBvcnQgY29uc3QgaXNUeXBlZEFycmF5ID0gKHBheWxvYWQpID0+IEFycmF5QnVmZmVyLmlzVmlldyhwYXlsb2FkKSAmJiAhKHBheWxvYWQgaW5zdGFuY2VvZiBEYXRhVmlldyk7XG5leHBvcnQgY29uc3QgaXNVUkwgPSAocGF5bG9hZCkgPT4gcGF5bG9hZCBpbnN0YW5jZW9mIFVSTDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWlzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/is.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/pathstringifier.js":
/*!********************************************************!*\
  !*** ./node_modules/superjson/dist/pathstringifier.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   escapeKey: () => (/* binding */ escapeKey),\n/* harmony export */   parsePath: () => (/* binding */ parsePath),\n/* harmony export */   stringifyPath: () => (/* binding */ stringifyPath)\n/* harmony export */ });\nconst escapeKey = (key) => key.replace(/\\./g, '\\\\.');\nconst stringifyPath = (path) => path\n    .map(String)\n    .map(escapeKey)\n    .join('.');\nconst parsePath = (string) => {\n    const result = [];\n    let segment = '';\n    for (let i = 0; i < string.length; i++) {\n        let char = string.charAt(i);\n        const isEscapedDot = char === '\\\\' && string.charAt(i + 1) === '.';\n        if (isEscapedDot) {\n            segment += '.';\n            i++;\n            continue;\n        }\n        const isEndOfSegment = char === '.';\n        if (isEndOfSegment) {\n            result.push(segment);\n            segment = '';\n            continue;\n        }\n        segment += char;\n    }\n    const lastSegment = segment;\n    result.push(lastSegment);\n    return result;\n};\n//# sourceMappingURL=pathstringifier.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvcGF0aHN0cmluZ2lmaWVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxvQkFBb0IsbUJBQW1CO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc3VwcmVldGhcXE9uZURyaXZlXFxEZXNrdG9wXFxhYmNkd2VyXFxOYXNoaXJhXFxub2RlX21vZHVsZXNcXHN1cGVyanNvblxcZGlzdFxccGF0aHN0cmluZ2lmaWVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBlc2NhcGVLZXkgPSAoa2V5KSA9PiBrZXkucmVwbGFjZSgvXFwuL2csICdcXFxcLicpO1xuZXhwb3J0IGNvbnN0IHN0cmluZ2lmeVBhdGggPSAocGF0aCkgPT4gcGF0aFxuICAgIC5tYXAoU3RyaW5nKVxuICAgIC5tYXAoZXNjYXBlS2V5KVxuICAgIC5qb2luKCcuJyk7XG5leHBvcnQgY29uc3QgcGFyc2VQYXRoID0gKHN0cmluZykgPT4ge1xuICAgIGNvbnN0IHJlc3VsdCA9IFtdO1xuICAgIGxldCBzZWdtZW50ID0gJyc7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzdHJpbmcubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgbGV0IGNoYXIgPSBzdHJpbmcuY2hhckF0KGkpO1xuICAgICAgICBjb25zdCBpc0VzY2FwZWREb3QgPSBjaGFyID09PSAnXFxcXCcgJiYgc3RyaW5nLmNoYXJBdChpICsgMSkgPT09ICcuJztcbiAgICAgICAgaWYgKGlzRXNjYXBlZERvdCkge1xuICAgICAgICAgICAgc2VnbWVudCArPSAnLic7XG4gICAgICAgICAgICBpKys7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBpc0VuZE9mU2VnbWVudCA9IGNoYXIgPT09ICcuJztcbiAgICAgICAgaWYgKGlzRW5kT2ZTZWdtZW50KSB7XG4gICAgICAgICAgICByZXN1bHQucHVzaChzZWdtZW50KTtcbiAgICAgICAgICAgIHNlZ21lbnQgPSAnJztcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIHNlZ21lbnQgKz0gY2hhcjtcbiAgICB9XG4gICAgY29uc3QgbGFzdFNlZ21lbnQgPSBzZWdtZW50O1xuICAgIHJlc3VsdC5wdXNoKGxhc3RTZWdtZW50KTtcbiAgICByZXR1cm4gcmVzdWx0O1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhdGhzdHJpbmdpZmllci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/pathstringifier.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/plainer.js":
/*!************************************************!*\
  !*** ./node_modules/superjson/dist/plainer.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyReferentialEqualityAnnotations: () => (/* binding */ applyReferentialEqualityAnnotations),\n/* harmony export */   applyValueAnnotations: () => (/* binding */ applyValueAnnotations),\n/* harmony export */   generateReferentialEqualityAnnotations: () => (/* binding */ generateReferentialEqualityAnnotations),\n/* harmony export */   walker: () => (/* binding */ walker)\n/* harmony export */ });\n/* harmony import */ var _is_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is.js */ \"(ssr)/./node_modules/superjson/dist/is.js\");\n/* harmony import */ var _pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pathstringifier.js */ \"(ssr)/./node_modules/superjson/dist/pathstringifier.js\");\n/* harmony import */ var _transformer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./transformer.js */ \"(ssr)/./node_modules/superjson/dist/transformer.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/superjson/dist/util.js\");\n/* harmony import */ var _accessDeep_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./accessDeep.js */ \"(ssr)/./node_modules/superjson/dist/accessDeep.js\");\n\n\n\n\n\n\nfunction traverse(tree, walker, origin = []) {\n    if (!tree) {\n        return;\n    }\n    if (!(0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(tree)) {\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(tree, (subtree, key) => traverse(subtree, walker, [...origin, ...(0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.parsePath)(key)]));\n        return;\n    }\n    const [nodeValue, children] = tree;\n    if (children) {\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(children, (child, key) => {\n            traverse(child, walker, [...origin, ...(0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.parsePath)(key)]);\n        });\n    }\n    walker(nodeValue, origin);\n}\nfunction applyValueAnnotations(plain, annotations, superJson) {\n    traverse(annotations, (type, path) => {\n        plain = (0,_accessDeep_js__WEBPACK_IMPORTED_MODULE_4__.setDeep)(plain, path, v => (0,_transformer_js__WEBPACK_IMPORTED_MODULE_2__.untransformValue)(v, type, superJson));\n    });\n    return plain;\n}\nfunction applyReferentialEqualityAnnotations(plain, annotations) {\n    function apply(identicalPaths, path) {\n        const object = (0,_accessDeep_js__WEBPACK_IMPORTED_MODULE_4__.getDeep)(plain, (0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.parsePath)(path));\n        identicalPaths.map(_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.parsePath).forEach(identicalObjectPath => {\n            plain = (0,_accessDeep_js__WEBPACK_IMPORTED_MODULE_4__.setDeep)(plain, identicalObjectPath, () => object);\n        });\n    }\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(annotations)) {\n        const [root, other] = annotations;\n        root.forEach(identicalPath => {\n            plain = (0,_accessDeep_js__WEBPACK_IMPORTED_MODULE_4__.setDeep)(plain, (0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.parsePath)(identicalPath), () => plain);\n        });\n        if (other) {\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(other, apply);\n        }\n    }\n    else {\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(annotations, apply);\n    }\n    return plain;\n}\nconst isDeep = (object, superJson) => (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(object) ||\n    (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(object) ||\n    (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isMap)(object) ||\n    (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isSet)(object) ||\n    (0,_transformer_js__WEBPACK_IMPORTED_MODULE_2__.isInstanceOfRegisteredClass)(object, superJson);\nfunction addIdentity(object, path, identities) {\n    const existingSet = identities.get(object);\n    if (existingSet) {\n        existingSet.push(path);\n    }\n    else {\n        identities.set(object, [path]);\n    }\n}\nfunction generateReferentialEqualityAnnotations(identitites, dedupe) {\n    const result = {};\n    let rootEqualityPaths = undefined;\n    identitites.forEach(paths => {\n        if (paths.length <= 1) {\n            return;\n        }\n        // if we're not deduping, all of these objects continue existing.\n        // putting the shortest path first makes it easier to parse for humans\n        // if we're deduping though, only the first entry will still exist, so we can't do this optimisation.\n        if (!dedupe) {\n            paths = paths\n                .map(path => path.map(String))\n                .sort((a, b) => a.length - b.length);\n        }\n        const [representativePath, ...identicalPaths] = paths;\n        if (representativePath.length === 0) {\n            rootEqualityPaths = identicalPaths.map(_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.stringifyPath);\n        }\n        else {\n            result[(0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.stringifyPath)(representativePath)] = identicalPaths.map(_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.stringifyPath);\n        }\n    });\n    if (rootEqualityPaths) {\n        if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isEmptyObject)(result)) {\n            return [rootEqualityPaths];\n        }\n        else {\n            return [rootEqualityPaths, result];\n        }\n    }\n    else {\n        return (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isEmptyObject)(result) ? undefined : result;\n    }\n}\nconst walker = (object, identities, superJson, dedupe, path = [], objectsInThisPath = [], seenObjects = new Map()) => {\n    const primitive = (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isPrimitive)(object);\n    if (!primitive) {\n        addIdentity(object, path, identities);\n        const seen = seenObjects.get(object);\n        if (seen) {\n            // short-circuit result if we've seen this object before\n            return dedupe\n                ? {\n                    transformedValue: null,\n                }\n                : seen;\n        }\n    }\n    if (!isDeep(object, superJson)) {\n        const transformed = (0,_transformer_js__WEBPACK_IMPORTED_MODULE_2__.transformValue)(object, superJson);\n        const result = transformed\n            ? {\n                transformedValue: transformed.value,\n                annotations: [transformed.type],\n            }\n            : {\n                transformedValue: object,\n            };\n        if (!primitive) {\n            seenObjects.set(object, result);\n        }\n        return result;\n    }\n    if ((0,_util_js__WEBPACK_IMPORTED_MODULE_3__.includes)(objectsInThisPath, object)) {\n        // prevent circular references\n        return {\n            transformedValue: null,\n        };\n    }\n    const transformationResult = (0,_transformer_js__WEBPACK_IMPORTED_MODULE_2__.transformValue)(object, superJson);\n    const transformed = transformationResult?.value ?? object;\n    const transformedValue = (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(transformed) ? [] : {};\n    const innerAnnotations = {};\n    (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(transformed, (value, index) => {\n        if (index === '__proto__' ||\n            index === 'constructor' ||\n            index === 'prototype') {\n            throw new Error(`Detected property ${index}. This is a prototype pollution risk, please remove it from your object.`);\n        }\n        const recursiveResult = walker(value, identities, superJson, dedupe, [...path, index], [...objectsInThisPath, object], seenObjects);\n        transformedValue[index] = recursiveResult.transformedValue;\n        if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(recursiveResult.annotations)) {\n            innerAnnotations[index] = recursiveResult.annotations;\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(recursiveResult.annotations)) {\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(recursiveResult.annotations, (tree, key) => {\n                innerAnnotations[(0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.escapeKey)(index) + '.' + key] = tree;\n            });\n        }\n    });\n    const result = (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isEmptyObject)(innerAnnotations)\n        ? {\n            transformedValue,\n            annotations: !!transformationResult\n                ? [transformationResult.type]\n                : undefined,\n        }\n        : {\n            transformedValue,\n            annotations: !!transformationResult\n                ? [transformationResult.type, innerAnnotations]\n                : innerAnnotations,\n        };\n    if (!primitive) {\n        seenObjects.set(object, result);\n    }\n    return result;\n};\n//# sourceMappingURL=plainer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvcGxhaW5lci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBNEY7QUFDNUI7QUFDa0M7QUFDcEQ7QUFDRztBQUNFO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUywrQ0FBTztBQUNoQixRQUFRLGlEQUFPLGtFQUFrRSw4REFBUztBQUMxRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsaURBQU87QUFDZixtREFBbUQsOERBQVM7QUFDNUQsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSxnQkFBZ0IsdURBQU8sbUJBQW1CLGlFQUFnQjtBQUMxRCxLQUFLO0FBQ0w7QUFDQTtBQUNPO0FBQ1A7QUFDQSx1QkFBdUIsdURBQU8sUUFBUSw4REFBUztBQUMvQywyQkFBMkIsMERBQVM7QUFDcEMsb0JBQW9CLHVEQUFPO0FBQzNCLFNBQVM7QUFDVDtBQUNBLFFBQVEsK0NBQU87QUFDZjtBQUNBO0FBQ0Esb0JBQW9CLHVEQUFPLFFBQVEsOERBQVM7QUFDNUMsU0FBUztBQUNUO0FBQ0EsWUFBWSxpREFBTztBQUNuQjtBQUNBO0FBQ0E7QUFDQSxRQUFRLGlEQUFPO0FBQ2Y7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLHFEQUFhO0FBQ25ELElBQUksK0NBQU87QUFDWCxJQUFJLDZDQUFLO0FBQ1QsSUFBSSw2Q0FBSztBQUNULElBQUksNEVBQTJCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtREFBbUQsOERBQWE7QUFDaEU7QUFDQTtBQUNBLG1CQUFtQixrRUFBYSwyQ0FBMkMsOERBQWE7QUFDeEY7QUFDQSxLQUFLO0FBQ0w7QUFDQSxZQUFZLHFEQUFhO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxxREFBYTtBQUM1QjtBQUNBO0FBQ087QUFDUCxzQkFBc0IsbURBQVc7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsK0RBQWM7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLGtEQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsK0RBQWM7QUFDL0M7QUFDQSw2QkFBNkIsK0NBQU87QUFDcEM7QUFDQSxJQUFJLGlEQUFPO0FBQ1g7QUFDQTtBQUNBO0FBQ0EsaURBQWlELE1BQU07QUFDdkQ7QUFDQTtBQUNBO0FBQ0EsWUFBWSwrQ0FBTztBQUNuQjtBQUNBO0FBQ0EsaUJBQWlCLHFEQUFhO0FBQzlCLFlBQVksaURBQU87QUFDbkIsaUNBQWlDLDhEQUFTO0FBQzFDLGFBQWE7QUFDYjtBQUNBLEtBQUs7QUFDTCxtQkFBbUIscURBQWE7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHN1cHJlZXRoXFxPbmVEcml2ZVxcRGVza3RvcFxcYWJjZHdlclxcTmFzaGlyYVxcbm9kZV9tb2R1bGVzXFxzdXBlcmpzb25cXGRpc3RcXHBsYWluZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNBcnJheSwgaXNFbXB0eU9iamVjdCwgaXNNYXAsIGlzUGxhaW5PYmplY3QsIGlzUHJpbWl0aXZlLCBpc1NldCwgfSBmcm9tICcuL2lzLmpzJztcbmltcG9ydCB7IGVzY2FwZUtleSwgc3RyaW5naWZ5UGF0aCB9IGZyb20gJy4vcGF0aHN0cmluZ2lmaWVyLmpzJztcbmltcG9ydCB7IGlzSW5zdGFuY2VPZlJlZ2lzdGVyZWRDbGFzcywgdHJhbnNmb3JtVmFsdWUsIHVudHJhbnNmb3JtVmFsdWUsIH0gZnJvbSAnLi90cmFuc2Zvcm1lci5qcyc7XG5pbXBvcnQgeyBpbmNsdWRlcywgZm9yRWFjaCB9IGZyb20gJy4vdXRpbC5qcyc7XG5pbXBvcnQgeyBwYXJzZVBhdGggfSBmcm9tICcuL3BhdGhzdHJpbmdpZmllci5qcyc7XG5pbXBvcnQgeyBnZXREZWVwLCBzZXREZWVwIH0gZnJvbSAnLi9hY2Nlc3NEZWVwLmpzJztcbmZ1bmN0aW9uIHRyYXZlcnNlKHRyZWUsIHdhbGtlciwgb3JpZ2luID0gW10pIHtcbiAgICBpZiAoIXRyZWUpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBpZiAoIWlzQXJyYXkodHJlZSkpIHtcbiAgICAgICAgZm9yRWFjaCh0cmVlLCAoc3VidHJlZSwga2V5KSA9PiB0cmF2ZXJzZShzdWJ0cmVlLCB3YWxrZXIsIFsuLi5vcmlnaW4sIC4uLnBhcnNlUGF0aChrZXkpXSkpO1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIGNvbnN0IFtub2RlVmFsdWUsIGNoaWxkcmVuXSA9IHRyZWU7XG4gICAgaWYgKGNoaWxkcmVuKSB7XG4gICAgICAgIGZvckVhY2goY2hpbGRyZW4sIChjaGlsZCwga2V5KSA9PiB7XG4gICAgICAgICAgICB0cmF2ZXJzZShjaGlsZCwgd2Fsa2VyLCBbLi4ub3JpZ2luLCAuLi5wYXJzZVBhdGgoa2V5KV0pO1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgd2Fsa2VyKG5vZGVWYWx1ZSwgb3JpZ2luKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBhcHBseVZhbHVlQW5ub3RhdGlvbnMocGxhaW4sIGFubm90YXRpb25zLCBzdXBlckpzb24pIHtcbiAgICB0cmF2ZXJzZShhbm5vdGF0aW9ucywgKHR5cGUsIHBhdGgpID0+IHtcbiAgICAgICAgcGxhaW4gPSBzZXREZWVwKHBsYWluLCBwYXRoLCB2ID0+IHVudHJhbnNmb3JtVmFsdWUodiwgdHlwZSwgc3VwZXJKc29uKSk7XG4gICAgfSk7XG4gICAgcmV0dXJuIHBsYWluO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGFwcGx5UmVmZXJlbnRpYWxFcXVhbGl0eUFubm90YXRpb25zKHBsYWluLCBhbm5vdGF0aW9ucykge1xuICAgIGZ1bmN0aW9uIGFwcGx5KGlkZW50aWNhbFBhdGhzLCBwYXRoKSB7XG4gICAgICAgIGNvbnN0IG9iamVjdCA9IGdldERlZXAocGxhaW4sIHBhcnNlUGF0aChwYXRoKSk7XG4gICAgICAgIGlkZW50aWNhbFBhdGhzLm1hcChwYXJzZVBhdGgpLmZvckVhY2goaWRlbnRpY2FsT2JqZWN0UGF0aCA9PiB7XG4gICAgICAgICAgICBwbGFpbiA9IHNldERlZXAocGxhaW4sIGlkZW50aWNhbE9iamVjdFBhdGgsICgpID0+IG9iamVjdCk7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBpZiAoaXNBcnJheShhbm5vdGF0aW9ucykpIHtcbiAgICAgICAgY29uc3QgW3Jvb3QsIG90aGVyXSA9IGFubm90YXRpb25zO1xuICAgICAgICByb290LmZvckVhY2goaWRlbnRpY2FsUGF0aCA9PiB7XG4gICAgICAgICAgICBwbGFpbiA9IHNldERlZXAocGxhaW4sIHBhcnNlUGF0aChpZGVudGljYWxQYXRoKSwgKCkgPT4gcGxhaW4pO1xuICAgICAgICB9KTtcbiAgICAgICAgaWYgKG90aGVyKSB7XG4gICAgICAgICAgICBmb3JFYWNoKG90aGVyLCBhcHBseSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIGZvckVhY2goYW5ub3RhdGlvbnMsIGFwcGx5KTtcbiAgICB9XG4gICAgcmV0dXJuIHBsYWluO1xufVxuY29uc3QgaXNEZWVwID0gKG9iamVjdCwgc3VwZXJKc29uKSA9PiBpc1BsYWluT2JqZWN0KG9iamVjdCkgfHxcbiAgICBpc0FycmF5KG9iamVjdCkgfHxcbiAgICBpc01hcChvYmplY3QpIHx8XG4gICAgaXNTZXQob2JqZWN0KSB8fFxuICAgIGlzSW5zdGFuY2VPZlJlZ2lzdGVyZWRDbGFzcyhvYmplY3QsIHN1cGVySnNvbik7XG5mdW5jdGlvbiBhZGRJZGVudGl0eShvYmplY3QsIHBhdGgsIGlkZW50aXRpZXMpIHtcbiAgICBjb25zdCBleGlzdGluZ1NldCA9IGlkZW50aXRpZXMuZ2V0KG9iamVjdCk7XG4gICAgaWYgKGV4aXN0aW5nU2V0KSB7XG4gICAgICAgIGV4aXN0aW5nU2V0LnB1c2gocGF0aCk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICBpZGVudGl0aWVzLnNldChvYmplY3QsIFtwYXRoXSk7XG4gICAgfVxufVxuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlUmVmZXJlbnRpYWxFcXVhbGl0eUFubm90YXRpb25zKGlkZW50aXRpdGVzLCBkZWR1cGUpIHtcbiAgICBjb25zdCByZXN1bHQgPSB7fTtcbiAgICBsZXQgcm9vdEVxdWFsaXR5UGF0aHMgPSB1bmRlZmluZWQ7XG4gICAgaWRlbnRpdGl0ZXMuZm9yRWFjaChwYXRocyA9PiB7XG4gICAgICAgIGlmIChwYXRocy5sZW5ndGggPD0gMSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIC8vIGlmIHdlJ3JlIG5vdCBkZWR1cGluZywgYWxsIG9mIHRoZXNlIG9iamVjdHMgY29udGludWUgZXhpc3RpbmcuXG4gICAgICAgIC8vIHB1dHRpbmcgdGhlIHNob3J0ZXN0IHBhdGggZmlyc3QgbWFrZXMgaXQgZWFzaWVyIHRvIHBhcnNlIGZvciBodW1hbnNcbiAgICAgICAgLy8gaWYgd2UncmUgZGVkdXBpbmcgdGhvdWdoLCBvbmx5IHRoZSBmaXJzdCBlbnRyeSB3aWxsIHN0aWxsIGV4aXN0LCBzbyB3ZSBjYW4ndCBkbyB0aGlzIG9wdGltaXNhdGlvbi5cbiAgICAgICAgaWYgKCFkZWR1cGUpIHtcbiAgICAgICAgICAgIHBhdGhzID0gcGF0aHNcbiAgICAgICAgICAgICAgICAubWFwKHBhdGggPT4gcGF0aC5tYXAoU3RyaW5nKSlcbiAgICAgICAgICAgICAgICAuc29ydCgoYSwgYikgPT4gYS5sZW5ndGggLSBiLmxlbmd0aCk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgW3JlcHJlc2VudGF0aXZlUGF0aCwgLi4uaWRlbnRpY2FsUGF0aHNdID0gcGF0aHM7XG4gICAgICAgIGlmIChyZXByZXNlbnRhdGl2ZVBhdGgubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICByb290RXF1YWxpdHlQYXRocyA9IGlkZW50aWNhbFBhdGhzLm1hcChzdHJpbmdpZnlQYXRoKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHJlc3VsdFtzdHJpbmdpZnlQYXRoKHJlcHJlc2VudGF0aXZlUGF0aCldID0gaWRlbnRpY2FsUGF0aHMubWFwKHN0cmluZ2lmeVBhdGgpO1xuICAgICAgICB9XG4gICAgfSk7XG4gICAgaWYgKHJvb3RFcXVhbGl0eVBhdGhzKSB7XG4gICAgICAgIGlmIChpc0VtcHR5T2JqZWN0KHJlc3VsdCkpIHtcbiAgICAgICAgICAgIHJldHVybiBbcm9vdEVxdWFsaXR5UGF0aHNdO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIFtyb290RXF1YWxpdHlQYXRocywgcmVzdWx0XTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIGlzRW1wdHlPYmplY3QocmVzdWx0KSA/IHVuZGVmaW5lZCA6IHJlc3VsdDtcbiAgICB9XG59XG5leHBvcnQgY29uc3Qgd2Fsa2VyID0gKG9iamVjdCwgaWRlbnRpdGllcywgc3VwZXJKc29uLCBkZWR1cGUsIHBhdGggPSBbXSwgb2JqZWN0c0luVGhpc1BhdGggPSBbXSwgc2Vlbk9iamVjdHMgPSBuZXcgTWFwKCkpID0+IHtcbiAgICBjb25zdCBwcmltaXRpdmUgPSBpc1ByaW1pdGl2ZShvYmplY3QpO1xuICAgIGlmICghcHJpbWl0aXZlKSB7XG4gICAgICAgIGFkZElkZW50aXR5KG9iamVjdCwgcGF0aCwgaWRlbnRpdGllcyk7XG4gICAgICAgIGNvbnN0IHNlZW4gPSBzZWVuT2JqZWN0cy5nZXQob2JqZWN0KTtcbiAgICAgICAgaWYgKHNlZW4pIHtcbiAgICAgICAgICAgIC8vIHNob3J0LWNpcmN1aXQgcmVzdWx0IGlmIHdlJ3ZlIHNlZW4gdGhpcyBvYmplY3QgYmVmb3JlXG4gICAgICAgICAgICByZXR1cm4gZGVkdXBlXG4gICAgICAgICAgICAgICAgPyB7XG4gICAgICAgICAgICAgICAgICAgIHRyYW5zZm9ybWVkVmFsdWU6IG51bGwsXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIDogc2VlbjtcbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAoIWlzRGVlcChvYmplY3QsIHN1cGVySnNvbikpIHtcbiAgICAgICAgY29uc3QgdHJhbnNmb3JtZWQgPSB0cmFuc2Zvcm1WYWx1ZShvYmplY3QsIHN1cGVySnNvbik7XG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IHRyYW5zZm9ybWVkXG4gICAgICAgICAgICA/IHtcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm1lZFZhbHVlOiB0cmFuc2Zvcm1lZC52YWx1ZSxcbiAgICAgICAgICAgICAgICBhbm5vdGF0aW9uczogW3RyYW5zZm9ybWVkLnR5cGVdLFxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgOiB7XG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtZWRWYWx1ZTogb2JqZWN0LFxuICAgICAgICAgICAgfTtcbiAgICAgICAgaWYgKCFwcmltaXRpdmUpIHtcbiAgICAgICAgICAgIHNlZW5PYmplY3RzLnNldChvYmplY3QsIHJlc3VsdCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9XG4gICAgaWYgKGluY2x1ZGVzKG9iamVjdHNJblRoaXNQYXRoLCBvYmplY3QpKSB7XG4gICAgICAgIC8vIHByZXZlbnQgY2lyY3VsYXIgcmVmZXJlbmNlc1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdHJhbnNmb3JtZWRWYWx1ZTogbnVsbCxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgY29uc3QgdHJhbnNmb3JtYXRpb25SZXN1bHQgPSB0cmFuc2Zvcm1WYWx1ZShvYmplY3QsIHN1cGVySnNvbik7XG4gICAgY29uc3QgdHJhbnNmb3JtZWQgPSB0cmFuc2Zvcm1hdGlvblJlc3VsdD8udmFsdWUgPz8gb2JqZWN0O1xuICAgIGNvbnN0IHRyYW5zZm9ybWVkVmFsdWUgPSBpc0FycmF5KHRyYW5zZm9ybWVkKSA/IFtdIDoge307XG4gICAgY29uc3QgaW5uZXJBbm5vdGF0aW9ucyA9IHt9O1xuICAgIGZvckVhY2godHJhbnNmb3JtZWQsICh2YWx1ZSwgaW5kZXgpID0+IHtcbiAgICAgICAgaWYgKGluZGV4ID09PSAnX19wcm90b19fJyB8fFxuICAgICAgICAgICAgaW5kZXggPT09ICdjb25zdHJ1Y3RvcicgfHxcbiAgICAgICAgICAgIGluZGV4ID09PSAncHJvdG90eXBlJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBEZXRlY3RlZCBwcm9wZXJ0eSAke2luZGV4fS4gVGhpcyBpcyBhIHByb3RvdHlwZSBwb2xsdXRpb24gcmlzaywgcGxlYXNlIHJlbW92ZSBpdCBmcm9tIHlvdXIgb2JqZWN0LmApO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHJlY3Vyc2l2ZVJlc3VsdCA9IHdhbGtlcih2YWx1ZSwgaWRlbnRpdGllcywgc3VwZXJKc29uLCBkZWR1cGUsIFsuLi5wYXRoLCBpbmRleF0sIFsuLi5vYmplY3RzSW5UaGlzUGF0aCwgb2JqZWN0XSwgc2Vlbk9iamVjdHMpO1xuICAgICAgICB0cmFuc2Zvcm1lZFZhbHVlW2luZGV4XSA9IHJlY3Vyc2l2ZVJlc3VsdC50cmFuc2Zvcm1lZFZhbHVlO1xuICAgICAgICBpZiAoaXNBcnJheShyZWN1cnNpdmVSZXN1bHQuYW5ub3RhdGlvbnMpKSB7XG4gICAgICAgICAgICBpbm5lckFubm90YXRpb25zW2luZGV4XSA9IHJlY3Vyc2l2ZVJlc3VsdC5hbm5vdGF0aW9ucztcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChpc1BsYWluT2JqZWN0KHJlY3Vyc2l2ZVJlc3VsdC5hbm5vdGF0aW9ucykpIHtcbiAgICAgICAgICAgIGZvckVhY2gocmVjdXJzaXZlUmVzdWx0LmFubm90YXRpb25zLCAodHJlZSwga2V5KSA9PiB7XG4gICAgICAgICAgICAgICAgaW5uZXJBbm5vdGF0aW9uc1tlc2NhcGVLZXkoaW5kZXgpICsgJy4nICsga2V5XSA9IHRyZWU7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgIH0pO1xuICAgIGNvbnN0IHJlc3VsdCA9IGlzRW1wdHlPYmplY3QoaW5uZXJBbm5vdGF0aW9ucylcbiAgICAgICAgPyB7XG4gICAgICAgICAgICB0cmFuc2Zvcm1lZFZhbHVlLFxuICAgICAgICAgICAgYW5ub3RhdGlvbnM6ICEhdHJhbnNmb3JtYXRpb25SZXN1bHRcbiAgICAgICAgICAgICAgICA/IFt0cmFuc2Zvcm1hdGlvblJlc3VsdC50eXBlXVxuICAgICAgICAgICAgICAgIDogdW5kZWZpbmVkLFxuICAgICAgICB9XG4gICAgICAgIDoge1xuICAgICAgICAgICAgdHJhbnNmb3JtZWRWYWx1ZSxcbiAgICAgICAgICAgIGFubm90YXRpb25zOiAhIXRyYW5zZm9ybWF0aW9uUmVzdWx0XG4gICAgICAgICAgICAgICAgPyBbdHJhbnNmb3JtYXRpb25SZXN1bHQudHlwZSwgaW5uZXJBbm5vdGF0aW9uc11cbiAgICAgICAgICAgICAgICA6IGlubmVyQW5ub3RhdGlvbnMsXG4gICAgICAgIH07XG4gICAgaWYgKCFwcmltaXRpdmUpIHtcbiAgICAgICAgc2Vlbk9iamVjdHMuc2V0KG9iamVjdCwgcmVzdWx0KTtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wbGFpbmVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/plainer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/registry.js":
/*!*************************************************!*\
  !*** ./node_modules/superjson/dist/registry.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Registry: () => (/* binding */ Registry)\n/* harmony export */ });\n/* harmony import */ var _double_indexed_kv_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./double-indexed-kv.js */ \"(ssr)/./node_modules/superjson/dist/double-indexed-kv.js\");\n\nclass Registry {\n    constructor(generateIdentifier) {\n        this.generateIdentifier = generateIdentifier;\n        this.kv = new _double_indexed_kv_js__WEBPACK_IMPORTED_MODULE_0__.DoubleIndexedKV();\n    }\n    register(value, identifier) {\n        if (this.kv.getByValue(value)) {\n            return;\n        }\n        if (!identifier) {\n            identifier = this.generateIdentifier(value);\n        }\n        this.kv.set(identifier, value);\n    }\n    clear() {\n        this.kv.clear();\n    }\n    getIdentifier(value) {\n        return this.kv.getByValue(value);\n    }\n    getValue(identifier) {\n        return this.kv.getByKey(identifier);\n    }\n}\n//# sourceMappingURL=registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvcmVnaXN0cnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUQ7QUFDbEQ7QUFDUDtBQUNBO0FBQ0Esc0JBQXNCLGtFQUFlO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzdXByZWV0aFxcT25lRHJpdmVcXERlc2t0b3BcXGFiY2R3ZXJcXE5hc2hpcmFcXG5vZGVfbW9kdWxlc1xcc3VwZXJqc29uXFxkaXN0XFxyZWdpc3RyeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBEb3VibGVJbmRleGVkS1YgfSBmcm9tICcuL2RvdWJsZS1pbmRleGVkLWt2LmpzJztcbmV4cG9ydCBjbGFzcyBSZWdpc3RyeSB7XG4gICAgY29uc3RydWN0b3IoZ2VuZXJhdGVJZGVudGlmaWVyKSB7XG4gICAgICAgIHRoaXMuZ2VuZXJhdGVJZGVudGlmaWVyID0gZ2VuZXJhdGVJZGVudGlmaWVyO1xuICAgICAgICB0aGlzLmt2ID0gbmV3IERvdWJsZUluZGV4ZWRLVigpO1xuICAgIH1cbiAgICByZWdpc3Rlcih2YWx1ZSwgaWRlbnRpZmllcikge1xuICAgICAgICBpZiAodGhpcy5rdi5nZXRCeVZhbHVlKHZhbHVlKSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGlmICghaWRlbnRpZmllcikge1xuICAgICAgICAgICAgaWRlbnRpZmllciA9IHRoaXMuZ2VuZXJhdGVJZGVudGlmaWVyKHZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmt2LnNldChpZGVudGlmaWVyLCB2YWx1ZSk7XG4gICAgfVxuICAgIGNsZWFyKCkge1xuICAgICAgICB0aGlzLmt2LmNsZWFyKCk7XG4gICAgfVxuICAgIGdldElkZW50aWZpZXIodmFsdWUpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMua3YuZ2V0QnlWYWx1ZSh2YWx1ZSk7XG4gICAgfVxuICAgIGdldFZhbHVlKGlkZW50aWZpZXIpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMua3YuZ2V0QnlLZXkoaWRlbnRpZmllcik7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVnaXN0cnkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/transformer.js":
/*!****************************************************!*\
  !*** ./node_modules/superjson/dist/transformer.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isInstanceOfRegisteredClass: () => (/* binding */ isInstanceOfRegisteredClass),\n/* harmony export */   transformValue: () => (/* binding */ transformValue),\n/* harmony export */   untransformValue: () => (/* binding */ untransformValue)\n/* harmony export */ });\n/* harmony import */ var _is_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is.js */ \"(ssr)/./node_modules/superjson/dist/is.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/superjson/dist/util.js\");\n\n\nfunction simpleTransformation(isApplicable, annotation, transform, untransform) {\n    return {\n        isApplicable,\n        annotation,\n        transform,\n        untransform,\n    };\n}\nconst simpleRules = [\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isUndefined, 'undefined', () => null, () => undefined),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isBigint, 'bigint', v => v.toString(), v => {\n        if (typeof BigInt !== 'undefined') {\n            return BigInt(v);\n        }\n        console.error('Please add a BigInt polyfill.');\n        return v;\n    }),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isDate, 'Date', v => v.toISOString(), v => new Date(v)),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isError, 'Error', (v, superJson) => {\n        const baseError = {\n            name: v.name,\n            message: v.message,\n        };\n        superJson.allowedErrorProps.forEach(prop => {\n            baseError[prop] = v[prop];\n        });\n        return baseError;\n    }, (v, superJson) => {\n        const e = new Error(v.message);\n        e.name = v.name;\n        e.stack = v.stack;\n        superJson.allowedErrorProps.forEach(prop => {\n            e[prop] = v[prop];\n        });\n        return e;\n    }),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isRegExp, 'regexp', v => '' + v, regex => {\n        const body = regex.slice(1, regex.lastIndexOf('/'));\n        const flags = regex.slice(regex.lastIndexOf('/') + 1);\n        return new RegExp(body, flags);\n    }),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isSet, 'set', \n    // (sets only exist in es6+)\n    // eslint-disable-next-line es5/no-es6-methods\n    v => [...v.values()], v => new Set(v)),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isMap, 'map', v => [...v.entries()], v => new Map(v)),\n    simpleTransformation((v) => (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isNaNValue)(v) || (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isInfinite)(v), 'number', v => {\n        if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isNaNValue)(v)) {\n            return 'NaN';\n        }\n        if (v > 0) {\n            return 'Infinity';\n        }\n        else {\n            return '-Infinity';\n        }\n    }, Number),\n    simpleTransformation((v) => v === 0 && 1 / v === -Infinity, 'number', () => {\n        return '-0';\n    }, Number),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isURL, 'URL', v => v.toString(), v => new URL(v)),\n];\nfunction compositeTransformation(isApplicable, annotation, transform, untransform) {\n    return {\n        isApplicable,\n        annotation,\n        transform,\n        untransform,\n    };\n}\nconst symbolRule = compositeTransformation((s, superJson) => {\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isSymbol)(s)) {\n        const isRegistered = !!superJson.symbolRegistry.getIdentifier(s);\n        return isRegistered;\n    }\n    return false;\n}, (s, superJson) => {\n    const identifier = superJson.symbolRegistry.getIdentifier(s);\n    return ['symbol', identifier];\n}, v => v.description, (_, a, superJson) => {\n    const value = superJson.symbolRegistry.getValue(a[1]);\n    if (!value) {\n        throw new Error('Trying to deserialize unknown symbol');\n    }\n    return value;\n});\nconst constructorToName = [\n    Int8Array,\n    Uint8Array,\n    Int16Array,\n    Uint16Array,\n    Int32Array,\n    Uint32Array,\n    Float32Array,\n    Float64Array,\n    Uint8ClampedArray,\n].reduce((obj, ctor) => {\n    obj[ctor.name] = ctor;\n    return obj;\n}, {});\nconst typedArrayRule = compositeTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isTypedArray, v => ['typed-array', v.constructor.name], v => [...v], (v, a) => {\n    const ctor = constructorToName[a[1]];\n    if (!ctor) {\n        throw new Error('Trying to deserialize unknown typed array');\n    }\n    return new ctor(v);\n});\nfunction isInstanceOfRegisteredClass(potentialClass, superJson) {\n    if (potentialClass?.constructor) {\n        const isRegistered = !!superJson.classRegistry.getIdentifier(potentialClass.constructor);\n        return isRegistered;\n    }\n    return false;\n}\nconst classRule = compositeTransformation(isInstanceOfRegisteredClass, (clazz, superJson) => {\n    const identifier = superJson.classRegistry.getIdentifier(clazz.constructor);\n    return ['class', identifier];\n}, (clazz, superJson) => {\n    const allowedProps = superJson.classRegistry.getAllowedProps(clazz.constructor);\n    if (!allowedProps) {\n        return { ...clazz };\n    }\n    const result = {};\n    allowedProps.forEach(prop => {\n        result[prop] = clazz[prop];\n    });\n    return result;\n}, (v, a, superJson) => {\n    const clazz = superJson.classRegistry.getValue(a[1]);\n    if (!clazz) {\n        throw new Error(`Trying to deserialize unknown class '${a[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);\n    }\n    return Object.assign(Object.create(clazz.prototype), v);\n});\nconst customRule = compositeTransformation((value, superJson) => {\n    return !!superJson.customTransformerRegistry.findApplicable(value);\n}, (value, superJson) => {\n    const transformer = superJson.customTransformerRegistry.findApplicable(value);\n    return ['custom', transformer.name];\n}, (value, superJson) => {\n    const transformer = superJson.customTransformerRegistry.findApplicable(value);\n    return transformer.serialize(value);\n}, (v, a, superJson) => {\n    const transformer = superJson.customTransformerRegistry.findByName(a[1]);\n    if (!transformer) {\n        throw new Error('Trying to deserialize unknown custom value');\n    }\n    return transformer.deserialize(v);\n});\nconst compositeRules = [classRule, symbolRule, customRule, typedArrayRule];\nconst transformValue = (value, superJson) => {\n    const applicableCompositeRule = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.findArr)(compositeRules, rule => rule.isApplicable(value, superJson));\n    if (applicableCompositeRule) {\n        return {\n            value: applicableCompositeRule.transform(value, superJson),\n            type: applicableCompositeRule.annotation(value, superJson),\n        };\n    }\n    const applicableSimpleRule = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.findArr)(simpleRules, rule => rule.isApplicable(value, superJson));\n    if (applicableSimpleRule) {\n        return {\n            value: applicableSimpleRule.transform(value, superJson),\n            type: applicableSimpleRule.annotation,\n        };\n    }\n    return undefined;\n};\nconst simpleRulesByAnnotation = {};\nsimpleRules.forEach(rule => {\n    simpleRulesByAnnotation[rule.annotation] = rule;\n});\nconst untransformValue = (json, type, superJson) => {\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(type)) {\n        switch (type[0]) {\n            case 'symbol':\n                return symbolRule.untransform(json, type, superJson);\n            case 'class':\n                return classRule.untransform(json, type, superJson);\n            case 'custom':\n                return customRule.untransform(json, type, superJson);\n            case 'typed-array':\n                return typedArrayRule.untransform(json, type, superJson);\n            default:\n                throw new Error('Unknown transformation: ' + type);\n        }\n    }\n    else {\n        const transformation = simpleRulesByAnnotation[type];\n        if (!transformation) {\n            throw new Error('Unknown transformation: ' + type);\n        }\n        return transformation.untransform(json, superJson);\n    }\n};\n//# sourceMappingURL=transformer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/transformer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/superjson/dist/util.js":
/*!*********************************************!*\
  !*** ./node_modules/superjson/dist/util.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   findArr: () => (/* binding */ findArr),\n/* harmony export */   forEach: () => (/* binding */ forEach),\n/* harmony export */   includes: () => (/* binding */ includes)\n/* harmony export */ });\nfunction valuesOfObj(record) {\n    if ('values' in Object) {\n        // eslint-disable-next-line es5/no-es6-methods\n        return Object.values(record);\n    }\n    const values = [];\n    // eslint-disable-next-line no-restricted-syntax\n    for (const key in record) {\n        if (record.hasOwnProperty(key)) {\n            values.push(record[key]);\n        }\n    }\n    return values;\n}\nfunction find(record, predicate) {\n    const values = valuesOfObj(record);\n    if ('find' in values) {\n        // eslint-disable-next-line es5/no-es6-methods\n        return values.find(predicate);\n    }\n    const valuesNotNever = values;\n    for (let i = 0; i < valuesNotNever.length; i++) {\n        const value = valuesNotNever[i];\n        if (predicate(value)) {\n            return value;\n        }\n    }\n    return undefined;\n}\nfunction forEach(record, run) {\n    Object.entries(record).forEach(([key, value]) => run(value, key));\n}\nfunction includes(arr, value) {\n    return arr.indexOf(value) !== -1;\n}\nfunction findArr(record, predicate) {\n    for (let i = 0; i < record.length; i++) {\n        const value = record[i];\n        if (predicate(value)) {\n            return value;\n        }\n    }\n    return undefined;\n}\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvdXRpbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLDJCQUEyQjtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNPO0FBQ1Asb0JBQW9CLG1CQUFtQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHN1cHJlZXRoXFxPbmVEcml2ZVxcRGVza3RvcFxcYWJjZHdlclxcTmFzaGlyYVxcbm9kZV9tb2R1bGVzXFxzdXBlcmpzb25cXGRpc3RcXHV0aWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gdmFsdWVzT2ZPYmoocmVjb3JkKSB7XG4gICAgaWYgKCd2YWx1ZXMnIGluIE9iamVjdCkge1xuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgZXM1L25vLWVzNi1tZXRob2RzXG4gICAgICAgIHJldHVybiBPYmplY3QudmFsdWVzKHJlY29yZCk7XG4gICAgfVxuICAgIGNvbnN0IHZhbHVlcyA9IFtdO1xuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1yZXN0cmljdGVkLXN5bnRheFxuICAgIGZvciAoY29uc3Qga2V5IGluIHJlY29yZCkge1xuICAgICAgICBpZiAocmVjb3JkLmhhc093blByb3BlcnR5KGtleSkpIHtcbiAgICAgICAgICAgIHZhbHVlcy5wdXNoKHJlY29yZFtrZXldKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdmFsdWVzO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGZpbmQocmVjb3JkLCBwcmVkaWNhdGUpIHtcbiAgICBjb25zdCB2YWx1ZXMgPSB2YWx1ZXNPZk9iaihyZWNvcmQpO1xuICAgIGlmICgnZmluZCcgaW4gdmFsdWVzKSB7XG4gICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBlczUvbm8tZXM2LW1ldGhvZHNcbiAgICAgICAgcmV0dXJuIHZhbHVlcy5maW5kKHByZWRpY2F0ZSk7XG4gICAgfVxuICAgIGNvbnN0IHZhbHVlc05vdE5ldmVyID0gdmFsdWVzO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdmFsdWVzTm90TmV2ZXIubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSB2YWx1ZXNOb3ROZXZlcltpXTtcbiAgICAgICAgaWYgKHByZWRpY2F0ZSh2YWx1ZSkpIHtcbiAgICAgICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdW5kZWZpbmVkO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGZvckVhY2gocmVjb3JkLCBydW4pIHtcbiAgICBPYmplY3QuZW50cmllcyhyZWNvcmQpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4gcnVuKHZhbHVlLCBrZXkpKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBpbmNsdWRlcyhhcnIsIHZhbHVlKSB7XG4gICAgcmV0dXJuIGFyci5pbmRleE9mKHZhbHVlKSAhPT0gLTE7XG59XG5leHBvcnQgZnVuY3Rpb24gZmluZEFycihyZWNvcmQsIHByZWRpY2F0ZSkge1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcmVjb3JkLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IHZhbHVlID0gcmVjb3JkW2ldO1xuICAgICAgICBpZiAocHJlZGljYXRlKHZhbHVlKSkge1xuICAgICAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB1bmRlZmluZWQ7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlsLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superjson/dist/util.js\n");

/***/ })

};
;